"""
Supabase database client
"""
import hashlib
import pandas as pd
from datetime import datetime
from typing import Optional, Dict, Any
from urllib.parse import urlparse

from src.utils.logging import get_logger

logger = get_logger(__name__)

try:
    from supabase import create_client
    SUPABASE_AVAILABLE = True
except ImportError:
    logger.warning("Supabase client not available. Install with: pip install supabase")
    SUPABASE_AVAILABLE = False


class SupabaseClient:
    """Client for Supabase database operations"""
    
    def __init__(self, url: str, key: str, domain: str):
        if not SUPABASE_AVAILABLE:
            raise ImportError("Supabase client not available")
        
        self.client = create_client(url, key)
        self.domain = domain
        self.site_id = self._get_or_create_site_id()
        self.db_id = self.site_id
    
    def _get_or_create_site_id(self) -> str:
        """Get or create a site ID for the domain"""
        domain_hash = hashlib.md5(self.domain.encode()).hexdigest()
        
        # Check if site exists
        response = self.client.table('sites').select('id').eq('domain', self.domain).execute()
        
        if response.data:
            return response.data[0]['id']
        
        # Create new site
        site_data = {
            'id': domain_hash,
            'domain': self.domain,
            'created_at': datetime.now().isoformat()
        }
        
        response = self.client.table('sites').insert(site_data).execute()
        return domain_hash
    
    def save_pages_data(self, df: pd.DataFrame) -> Optional[Any]:
        """Save pages data to Supabase"""
        if df.empty or self.db_id is None:
            return None
        
        # Add site_id and snapshot date
        df = df.copy()
        df['site_id'] = self.db_id
        df['snapshot_date'] = datetime.now().strftime('%Y-%m-%d')
        
        # Add URL hash if it doesn't exist
        if 'url_hash' not in df.columns:
            df['url_hash'] = df['URL'].apply(lambda x: hashlib.md5(x.encode()).hexdigest())
        
        # Replace NaN values with None
        df = df.replace({float('nan'): None})
        
        # Convert to records
        records = df.to_dict(orient='records')
        
        try:
            response = self.client.table('pages').upsert(
                records,
                on_conflict='site_id,url_hash,snapshot_date'
            ).execute()
            
            logger.info(f"Saved {len(records)} pages to Supabase")
            return response.data
        except Exception as e:
            logger.error(f"Error saving pages data: {e}")
            return None
    
    def save_gsc_keywords(self, df: pd.DataFrame) -> Optional[Any]:
        """Save GSC keywords data to Supabase"""
        if df.empty or self.db_id is None:
            return None
        
        # Add site_id
        df = df.copy()
        df['site_id'] = self.db_id
        
        # Add keyword hash
        if 'keyword_hash' not in df.columns:
            df['keyword_hash'] = df.apply(
                lambda row: hashlib.md5(f"{row.get('query', '')}{row.get('page', '')}{row.get('Month', '')}".encode()).hexdigest(),
                axis=1
            )
        
        # Replace NaN values with None
        df = df.replace({float('nan'): None})
        
        # Convert to records
        records = df.to_dict(orient='records')
        
        try:
            response = self.client.table('gsc_keywords').upsert(
                records,
                on_conflict='site_id,keyword_hash'
            ).execute()
            
            logger.info(f"Saved {len(records)} GSC keywords to Supabase")
            return response.data
        except Exception as e:
            logger.error(f"Error saving GSC keywords: {e}")
            return None
    
    def save_gsc_traffic(self, df: pd.DataFrame) -> Optional[Any]:
        """Save GSC traffic data to Supabase"""
        if df.empty or self.db_id is None:
            return None
        
        # Add site_id
        df = df.copy()
        df['site_id'] = self.db_id
        
        # Add traffic hash
        if 'traffic_hash' not in df.columns:
            df['traffic_hash'] = df.apply(
                lambda row: hashlib.md5(f"{row.get('page', '')}{row.get('Month', '')}".encode()).hexdigest(),
                axis=1
            )
        
        # Replace NaN values with None
        df = df.replace({float('nan'): None})
        
        # Convert to records
        records = df.to_dict(orient='records')
        
        try:
            response = self.client.table('gsc_traffic').upsert(
                records,
                on_conflict='site_id,traffic_hash'
            ).execute()
            
            logger.info(f"Saved {len(records)} GSC traffic records to Supabase")
            return response.data
        except Exception as e:
            logger.error(f"Error saving GSC traffic: {e}")
            return None

    def save_internal_links(self, df: pd.DataFrame) -> Optional[Any]:
        """Save internal links data to Supabase"""
        if df.empty or self.db_id is None:
            return None

        # Add site_id and snapshot date
        df = df.copy()
        df['site_id'] = self.db_id
        df['snapshot_date'] = datetime.now().strftime('%Y-%m-%d')

        # Add link_hash if it doesn't exist
        if 'link_hash' not in df.columns:
            df['link_hash'] = df.apply(
                lambda row: hashlib.md5(f"{row['URL']}|{row['Target Hyperlink']}".encode()).hexdigest(),
                axis=1
            )

        # Replace NaN values with None
        df = df.replace({float('nan'): None})

        # Convert to records
        records = df.to_dict(orient='records')

        try:
            response = self.client.table('internal_links').upsert(
                records,
                on_conflict='site_id,link_hash,snapshot_date'
            ).execute()

            logger.info(f"Saved {len(records)} internal links to Supabase")
            return response.data
        except Exception as e:
            logger.error(f"Error saving internal links: {e}")
            return None

    def save_ga_data(self, df: pd.DataFrame) -> Optional[Any]:
        """Save Google Analytics data to Supabase"""
        if df.empty or self.db_id is None:
            return None

        # Add site_id
        df = df.copy()
        df['site_id'] = self.db_id

        # Add GA hash
        if 'ga_hash' not in df.columns:
            df['ga_hash'] = df.apply(
                lambda row: hashlib.md5(f"{row.get('pagePath', '')}{row.get('Month', '')}".encode()).hexdigest(),
                axis=1
            )

        # Replace NaN values with None
        df = df.replace({float('nan'): None})

        # Convert to records
        records = df.to_dict(orient='records')

        try:
            response = self.client.table('ga_data').upsert(
                records,
                on_conflict='site_id,ga_hash'
            ).execute()

            logger.info(f"Saved {len(records)} GA records to Supabase")
            return response.data
        except Exception as e:
            logger.error(f"Error saving GA data: {e}")
            return None

    def get_pages_data(self, date_filter: Optional[str] = None) -> pd.DataFrame:
        """Retrieve pages data from Supabase"""
        try:
            query = self.client.table('pages').select('*').eq('site_id', self.site_id)
            if date_filter:
                query = query.eq('snapshot_date', date_filter)

            response = query.execute()
            if response.data:
                return pd.DataFrame(response.data)
            return pd.DataFrame()
        except Exception as e:
            logger.error(f"Error retrieving pages data: {e}")
            return pd.DataFrame()

    def get_gsc_keywords(self, date_filter: Optional[str] = None) -> pd.DataFrame:
        """Retrieve GSC keywords data from Supabase"""
        try:
            query = self.client.table('gsc_keywords').select('*').eq('site_id', self.site_id)
            if date_filter:
                # For keywords, we might want to filter by month
                query = query.eq('Month', date_filter)

            response = query.execute()
            if response.data:
                return pd.DataFrame(response.data)
            return pd.DataFrame()
        except Exception as e:
            logger.error(f"Error retrieving GSC keywords: {e}")
            return pd.DataFrame()

    def get_gsc_traffic(self, date_filter: Optional[str] = None) -> pd.DataFrame:
        """Retrieve GSC traffic data from Supabase"""
        try:
            query = self.client.table('gsc_traffic').select('*').eq('site_id', self.site_id)
            if date_filter:
                query = query.eq('Month', date_filter)

            response = query.execute()
            if response.data:
                return pd.DataFrame(response.data)
            return pd.DataFrame()
        except Exception as e:
            logger.error(f"Error retrieving GSC traffic: {e}")
            return pd.DataFrame()

    def get_internal_links(self, date_filter: Optional[str] = None) -> pd.DataFrame:
        """Retrieve internal links data from Supabase"""
        try:
            query = self.client.table('internal_links').select('*').eq('site_id', self.site_id)
            if date_filter:
                query = query.eq('snapshot_date', date_filter)

            response = query.execute()
            if response.data:
                return pd.DataFrame(response.data)
            return pd.DataFrame()
        except Exception as e:
            logger.error(f"Error retrieving internal links: {e}")
            return pd.DataFrame()

    def get_ga_data(self, date_filter: Optional[str] = None) -> pd.DataFrame:
        """Retrieve Google Analytics data from Supabase"""
        try:
            query = self.client.table('ga_data').select('*').eq('site_id', self.site_id)
            if date_filter:
                query = query.eq('Month', date_filter)

            response = query.execute()
            if response.data:
                return pd.DataFrame(response.data)
            return pd.DataFrame()
        except Exception as e:
            logger.error(f"Error retrieving GA data: {e}")
            return pd.DataFrame()

    def generate_excel_report(self, output_dir: str, date_filter: Optional[str] = None,
                            include_raw_data: bool = True, include_keywords: bool = True,
                            include_traffic: bool = True, include_links: bool = True,
                            include_analytics: bool = True) -> str:
        """Generate Excel report from Supabase data"""
        from datetime import datetime
        import os

        logger.info(f"Generating Excel report from Supabase data for domain: {self.domain}")

        # Create Excel file path
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        date_suffix = f"_{date_filter}" if date_filter else ""
        excel_path = os.path.join(output_dir, f'seo_report_{self.domain.replace(".", "_")}{date_suffix}_{timestamp}.xlsx')

        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            # Pages data sheet
            if include_raw_data:
                pages_df = self.get_pages_data(date_filter)
                if not pages_df.empty:
                    # Clean up the data for Excel
                    display_columns = ['url', 'title', 'description', 'h1', 'text']
                    available_columns = [col for col in display_columns if col in pages_df.columns]
                    if available_columns:
                        pages_display = pages_df[available_columns]
                        pages_display.to_excel(writer, sheet_name='Pages', index=False)
                        logger.info(f"Added {len(pages_display)} pages to Excel report")

            # Keywords sheet
            if include_keywords:
                keywords_df = self.get_gsc_keywords(date_filter)
                if not keywords_df.empty:
                    # Clean up columns for display
                    display_columns = ['query', 'page', 'clicks', 'impressions', 'ctr', 'position', 'Month']
                    available_columns = [col for col in display_columns if col in keywords_df.columns]
                    if available_columns:
                        keywords_display = keywords_df[available_columns]
                        keywords_display.to_excel(writer, sheet_name='Keywords', index=False)
                        logger.info(f"Added {len(keywords_display)} keywords to Excel report")

            # Traffic sheet
            if include_traffic:
                traffic_df = self.get_gsc_traffic(date_filter)
                if not traffic_df.empty:
                    display_columns = ['page', 'clicks', 'impressions', 'ctr', 'position', 'Month']
                    available_columns = [col for col in display_columns if col in traffic_df.columns]
                    if available_columns:
                        traffic_display = traffic_df[available_columns]
                        traffic_display.to_excel(writer, sheet_name='Traffic', index=False)
                        logger.info(f"Added {len(traffic_display)} traffic records to Excel report")

            # Internal links sheet
            if include_links:
                links_df = self.get_internal_links(date_filter)
                if not links_df.empty:
                    display_columns = ['URL', 'Target Hyperlink', 'Anchor Text', 'Link Type', 'URL Topic', 'Target Title', 'Relevance Score']
                    available_columns = [col for col in display_columns if col in links_df.columns]
                    if available_columns:
                        links_display = links_df[available_columns]
                        links_display.to_excel(writer, sheet_name='Internal Links', index=False)
                        logger.info(f"Added {len(links_display)} internal links to Excel report")

            # Google Analytics sheet
            if include_analytics:
                ga_df = self.get_ga_data(date_filter)
                if not ga_df.empty:
                    display_columns = ['pagePath', 'sessions', 'pageviews', 'bounceRate', 'avgSessionDuration', 'Month']
                    available_columns = [col for col in display_columns if col in ga_df.columns]
                    if available_columns:
                        ga_display = ga_df[available_columns]
                        ga_display.to_excel(writer, sheet_name='Analytics', index=False)
                        logger.info(f"Added {len(ga_display)} GA records to Excel report")

            # Summary sheet
            summary_data = {
                'Metric': [
                    'Domain',
                    'Site ID',
                    'Report Generated',
                    'Date Filter',
                    'Total Pages',
                    'Total Keywords',
                    'Total Traffic Records',
                    'Total Internal Links',
                    'Total GA Records'
                ],
                'Value': [
                    self.domain,
                    self.site_id,
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    date_filter or 'All dates',
                    len(self.get_pages_data(date_filter)),
                    len(self.get_gsc_keywords(date_filter)),
                    len(self.get_gsc_traffic(date_filter)),
                    len(self.get_internal_links(date_filter)),
                    len(self.get_ga_data(date_filter))
                ]
            }

            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='Summary', index=False)

        logger.info(f"Excel report generated from Supabase data: {excel_path}")
        return excel_path
