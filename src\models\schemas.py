"""
Pydantic models for API request/response schemas
"""
from typing import Optional, List, Dict, Any, Union
from pydantic import BaseModel, Field, validator


class ConfigSchema(BaseModel):
    """Configuration schema for SEO analysis requests"""
    domain_property: str = Field(..., description="Domain property in GSC (e.g., https://example.com/)")
    ga_property_id: str = Field(..., description="Google Analytics property ID")
    service_account_file: str = Field(..., description="Path to Google service account JSON file")
    supabase_url: Optional[str] = Field(None, description="Supabase URL (optional - uses environment variable if not provided)")
    supabase_key: Optional[str] = Field(None, description="Supabase API key (optional - uses environment variable if not provided)")
    homepage: Optional[str] = Field(None, description="Homepage URL (defaults to domain_property)")
    start_date: Optional[str] = Field(None, description="Start date for data (YYYY-MM-DD)")
    end_date: Optional[str] = Field(None, description="End date for data (YYYY-MM-DD)")
    website_urls: Optional[List[str]] = Field(None, description="Specific URLs to crawl (optional)")
    wp_api_key: Optional[str] = Field(None, description="WordPress API key if available")

    @validator('start_date', 'end_date')
    def validate_date_format(cls, v):
        if v is not None:
            from src.config.settings import validate_date_format
            if not validate_date_format(v):
                raise ValueError('Date must be in YYYY-MM-DD format')
        return v


class ExcelRequestSchema(BaseModel):
    """Schema for Excel report generation requests from Supabase data"""
    domain: str = Field(..., description="Domain to generate report for")
    date: Optional[str] = Field(None, description="Specific snapshot date (YYYY-MM-DD)")
    supabase_url: Optional[str] = Field(None, description="Supabase URL (optional - uses environment variable if not provided)")
    supabase_key: Optional[str] = Field(None, description="Supabase API key (optional - uses environment variable if not provided)")
    include_raw_data: Optional[bool] = Field(True, description="Include raw crawl data in report")
    include_keywords: Optional[bool] = Field(True, description="Include GSC keywords data")
    include_traffic: Optional[bool] = Field(True, description="Include GSC traffic data")
    include_links: Optional[bool] = Field(True, description="Include internal links data")
    include_analytics: Optional[bool] = Field(True, description="Include Google Analytics data")

    @validator('date')
    def validate_date_format(cls, v):
        if v is not None:
            from src.config.settings import validate_date_format
            if not validate_date_format(v):
                raise ValueError('Date must be in YYYY-MM-DD format')
        return v


class TaskResponse(BaseModel):
    """Response schema for task creation"""
    task_id: str
    status: str
    message: str


class TaskStatus(BaseModel):
    """Schema for task status responses"""
    status: str
    progress: Optional[int] = None
    message: Optional[str] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class CrawlResult(BaseModel):
    """Schema for crawl results"""
    url: str
    title: str
    description: str
    h1: str
    text: str
    raw_html: Optional[str] = None


class InternalLink(BaseModel):
    """Schema for internal link data"""
    url: str = Field(alias="URL")
    target_hyperlink: str = Field(alias="Target Hyperlink")
    anchor_text: str = Field(alias="Anchor Text")
    link_type: str = Field(alias="Link Type")
    url_topic: str = Field(alias="URL Topic")
    target_title: str = Field(alias="Target Title")
    relevance_score: Optional[float] = Field(alias="Relevance Score")

    class Config:
        allow_population_by_field_name = True


class SiteDataSummary(BaseModel):
    """Schema for site data summary"""
    pages: int
    keywords: int
    traffic_records: int
    internal_links: int
    analytics_records: int
    total_records: int


class SiteConfiguration(BaseModel):
    """Schema for site configuration"""
    domain_property: str
    ga_property_id: str
    service_account_data: Optional[Dict[str, Any]] = None
    homepage: Optional[str] = None

class SiteInfo(BaseModel):
    """Schema for site information"""
    domain: str
    site_id: Union[str, int]  # Can be string or integer from database
    created_at: Optional[str] = None
    data_summary: SiteDataSummary
    available_dates: List[str]
    available_months: List[str]
    last_updated: Optional[str] = None
    configuration: Optional[SiteConfiguration] = None


class SitesListResponse(BaseModel):
    """Schema for sites list response"""
    sites: List[SiteInfo]
    total_sites: int


class ReAnalysisRequestSchema(BaseModel):
    """Schema for re-analysis requests using stored configuration"""
    site_id: Union[str, int]
    start_date: Optional[str] = None
    end_date: Optional[str] = None

    @validator('start_date', 'end_date')
    def validate_date_format(cls, v):
        if v:
            try:
                from datetime import datetime
                datetime.strptime(v, '%Y-%m-%d')
            except ValueError:
                raise ValueError('Date must be in YYYY-MM-DD format')
        return v


class UpdateSiteConfigSchema(BaseModel):
    """Schema for updating site configuration"""
    site_id: Union[str, int]
    domain_property: str = Field(..., description="Domain property in GSC (e.g., https://example.com/)")
    ga_property_id: str = Field(..., description="Google Analytics property ID")
    service_account_data: Dict[str, Any] = Field(..., description="Service account JSON data")
    homepage: Optional[str] = Field(None, description="Homepage URL (optional)")


class DeleteSiteDataSchema(BaseModel):
    """Schema for deleting site data"""
    site_id: Union[str, int]
    confirm_domain: str = Field(..., description="Domain name to confirm deletion")
    delete_all_data: bool = Field(True, description="Delete all data for this site")


class EnhancedExcelRequestSchema(BaseModel):
    """Enhanced schema for Excel report generation with site selection"""
    site_id: Optional[str] = Field(None, description="Site ID to generate report for (alternative to domain)")
    domain: Optional[str] = Field(None, description="Domain to generate report for (alternative to site_id)")
    start_date: Optional[str] = Field(None, description="Start date filter (YYYY-MM-DD)")
    end_date: Optional[str] = Field(None, description="End date filter (YYYY-MM-DD)")
    date: Optional[str] = Field(None, description="Specific snapshot date (YYYY-MM-DD) - legacy field")
    include_raw_data: Optional[bool] = Field(True, description="Include raw crawl data in report")
    include_keywords: Optional[bool] = Field(True, description="Include GSC keywords data")
    include_traffic: Optional[bool] = Field(True, description="Include GSC traffic data")
    include_links: Optional[bool] = Field(True, description="Include internal links data")
    include_analytics: Optional[bool] = Field(True, description="Include Google Analytics data")

    @validator('start_date', 'end_date', 'date')
    def validate_date_format(cls, v):
        if v is not None:
            from src.config.settings import validate_date_format
            if not validate_date_format(v):
                raise ValueError('Date must be in YYYY-MM-DD format')
        return v

    @validator('domain', always=True)
    def validate_site_identification(cls, v, values):
        site_id = values.get('site_id')
        if not site_id and not v:
            raise ValueError('Either site_id or domain must be provided')
        return v
