2025-06-30 03:49:18,264 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 03:49:33,807 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 03:49:33,808 - src.cli.main - INFO - Starting API server on 0.0.0.0:8000
2025-06-30 03:50:01,889 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 03:50:01,890 - src.cli.main - INFO - Starting API server on 0.0.0.0:8001
2025-06-30 04:09:00,594 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 04:09:00,595 - src.cli.main - INFO - Starting API server on 0.0.0.0:8001
2025-06-30 04:10:37,429 - src.api.routes - ERROR - Error generating Excel from Supabase for task 713ec8d3-8d06-482a-aaa5-b11346b7aa54
Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_transports\default.py", line 101, in map_httpcore_exceptions
    yield
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_transports\default.py", line 250, in handle_request
    resp = self._pool.handle_request(req)
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_sync\connection_pool.py", line 256, in handle_request
    raise exc from None
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_sync\connection_pool.py", line 236, in handle_request
    response = connection.handle_request(
        pool_request.request
    )
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_sync\connection.py", line 101, in handle_request
    raise exc
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_sync\connection.py", line 78, in handle_request
    stream = self._connect(request)
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_sync\connection.py", line 124, in _connect
    stream = self._network_backend.connect_tcp(**kwargs)
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_backends\sync.py", line 207, in connect_tcp
    with map_exceptions(exc_map):
         ~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1520.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\src\api\routes.py", line 80, in generate_excel_from_supabase
    supabase_client = SupabaseClient(
        url=supabase_url,
        key=supabase_key,
        domain=domain
    )
  File "C:\Gautam\Projects\Scraper\src\database\supabase_client.py", line 31, in __init__
    self.site_id = self._get_or_create_site_id()
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Gautam\Projects\Scraper\src\database\supabase_client.py", line 39, in _get_or_create_site_id
    response = self.client.table('sites').select('id').eq('domain', self.domain).execute()
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\postgrest\_sync\request_builder.py", line 57, in execute
    r = self.session.request(
        self.http_method,
    ...<3 lines>...
        headers=self.headers,
    )
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_client.py", line 825, in request
    return self.send(request, auth=auth, follow_redirects=follow_redirects)
           ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_client.py", line 914, in send
    response = self._send_handling_auth(
        request,
    ...<2 lines>...
        history=[],
    )
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_client.py", line 942, in _send_handling_auth
    response = self._send_handling_redirects(
        request,
        follow_redirects=follow_redirects,
        history=history,
    )
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_client.py", line 979, in _send_handling_redirects
    response = self._send_single_request(request)
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_client.py", line 1014, in _send_single_request
    response = transport.handle_request(request)
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_transports\default.py", line 249, in handle_request
    with map_httpcore_exceptions():
         ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1520.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_transports\default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-06-30 04:10:48,466 - src.services.analysis_service - ERROR - Error in SEO analysis for task 5618550b-d9ad-4f67-abbe-5a360b14e8be
Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\src\services\analysis_service.py", line 58, in run_analysis
    supabase_url = config['supabase_url']  # Required field
                   ~~~~~~^^^^^^^^^^^^^^^^
KeyError: 'supabase_url'
2025-06-30 04:10:48,468 - src.api.routes - ERROR - Error in background analysis task 5618550b-d9ad-4f67-abbe-5a360b14e8be
Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\src\api\routes.py", line 51, in run_seo_analysis
    await analysis_service.run_analysis(
    ...<2 lines>...
    )
  File "C:\Gautam\Projects\Scraper\src\services\analysis_service.py", line 58, in run_analysis
    supabase_url = config['supabase_url']  # Required field
                   ~~~~~~^^^^^^^^^^^^^^^^
KeyError: 'supabase_url'
2025-06-30 04:22:37,179 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 04:22:37,180 - __main__ - INFO - Starting API server on 0.0.0.0:8000
2025-06-30 04:23:02,182 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 04:23:29,975 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 04:23:46,678 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 04:27:33,757 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 04:28:15,693 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 04:30:33,754 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 04:45:17,449 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 04:45:27,017 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 04:45:27,747 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:45:28,451 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:45:28,725 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:45:28,966 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:45:29,227 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:45:29,464 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:45:29,696 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:45:29,950 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:45:30,231 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:45:30,466 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:46:26,303 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 04:46:26,537 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:46:26,776 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:46:27,029 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:46:27,266 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:46:27,508 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:46:27,747 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:46:27,985 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:46:28,237 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:46:28,488 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:46:28,737 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:46:44,944 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 04:47:01,576 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 04:47:01,849 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:47:02,109 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:47:02,387 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:47:02,635 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:47:02,898 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:47:03,135 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:47:03,405 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:47:03,676 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:47:03,919 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:47:04,167 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:47:12,322 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 04:47:12,801 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:47:13,153 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:47:13,507 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:47:13,879 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:00:38,288 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:00:38,325 - src.api.routes - ERROR - Error listing sites:
Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_transports\default.py", line 101, in map_httpcore_exceptions
    yield
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_transports\default.py", line 250, in handle_request
    resp = self._pool.handle_request(req)
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_sync\connection_pool.py", line 256, in handle_request
    raise exc from None
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_sync\connection_pool.py", line 236, in handle_request
    response = connection.handle_request(
        pool_request.request
    )
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_sync\connection.py", line 103, in handle_request
    return self._connection.handle_request(request)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_sync\http2.py", line 187, in handle_request
    raise exc
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_sync\http2.py", line 150, in handle_request
    status, headers = self._receive_response(
                      ~~~~~~~~~~~~~~~~~~~~~~^
        request=request, stream_id=stream_id
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_sync\http2.py", line 294, in _receive_response
    event = self._receive_stream_event(request, stream_id)
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_sync\http2.py", line 336, in _receive_stream_event
    self._receive_events(request, stream_id)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_sync\http2.py", line 364, in _receive_events
    events = self._read_incoming_data(request)
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_sync\http2.py", line 455, in _read_incoming_data
    raise exc
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_sync\http2.py", line 441, in _read_incoming_data
    data = self._network_stream.read(self.READ_NUM_BYTES, timeout)
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_backends\sync.py", line 126, in read
    with map_exceptions(exc_map):
         ~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1520.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ReadError: [WinError 10054] An existing connection was forcibly closed by the remote host

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\src\api\routes.py", line 599, in list_sites
    pages_dates = client.table('pages').select('snapshot_date').eq('site_id', site_id).execute().data or []
                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\postgrest\_sync\request_builder.py", line 57, in execute
    r = self.session.request(
        self.http_method,
    ...<3 lines>...
        headers=self.headers,
    )
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_client.py", line 825, in request
    return self.send(request, auth=auth, follow_redirects=follow_redirects)
           ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_client.py", line 914, in send
    response = self._send_handling_auth(
        request,
    ...<2 lines>...
        history=[],
    )
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_client.py", line 942, in _send_handling_auth
    response = self._send_handling_redirects(
        request,
        follow_redirects=follow_redirects,
        history=history,
    )
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_client.py", line 979, in _send_handling_redirects
    response = self._send_single_request(request)
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_client.py", line 1014, in _send_single_request
    response = transport.handle_request(request)
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_transports\default.py", line 249, in handle_request
    with map_httpcore_exceptions():
         ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1520.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_transports\default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ReadError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-06-30 05:02:13,306 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 05:02:13,952 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:02:14,182 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:02:14,422 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:02:15,080 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:02:15,316 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:02:15,559 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:02:15,801 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:02:16,043 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:02:16,281 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:02:16,519 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:02:18,214 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 05:02:18,455 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=%2A&site_id=eq.1&snapshot_date=gte.2024-01-01&snapshot_date=lte.2024-12-31 "HTTP/2 200 OK"
2025-06-30 05:09:34,079 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 05:10:34,624 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 05:10:34,878 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:35,138 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:35,372 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:35,616 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:35,855 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:36,096 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:36,338 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:36,591 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:36,829 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:37,068 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:56,602 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 05:10:56,845 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:57,086 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:57,321 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:57,560 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:57,799 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:58,027 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:58,262 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:58,506 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:58,746 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:58,987 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:11:40,755 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 05:13:28,340 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 05:33:13,380 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 05:33:26,836 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 05:33:27,555 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:27,794 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:28,037 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:28,681 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:29,338 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:29,580 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:29,823 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:30,060 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:30,298 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:30,538 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:43,794 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 05:33:44,043 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:44,278 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:44,514 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:44,754 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:45,000 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:45,238 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:45,474 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:45,710 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:45,950 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:46,190 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:42:12,804 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 05:42:39,576 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 05:42:39,813 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:42:40,056 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:42:40,292 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:42:40,534 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:42:40,773 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:42:41,007 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:42:41,246 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:42:41,481 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:42:41,718 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:42:41,956 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:43:11,896 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 05:43:32,614 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 05:43:32,868 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:43:33,109 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:43:33,354 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:43:33,596 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:43:33,838 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:43:34,076 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:43:34,315 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:43:34,557 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:43:34,800 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:43:35,039 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:44:04,506 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 05:44:04,745 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:44:04,985 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:44:05,222 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:44:05,458 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:44:05,699 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:44:05,937 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:44:06,172 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:44:06,406 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:44:06,645 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:44:06,881 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:14,841 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 05:46:15,076 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:15,313 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:15,549 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:15,784 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:16,020 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:16,261 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:16,501 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:16,736 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:16,974 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:17,217 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:31,164 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 05:46:31,397 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:31,634 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:31,880 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:32,114 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:32,348 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:32,582 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:32,816 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:33,052 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:33,287 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:33,524 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:47:27,642 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 05:47:27,880 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:47:28,118 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:47:28,353 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:47:28,591 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:47:28,828 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:47:29,065 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:47:29,304 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:47:29,540 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:47:29,777 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:47:30,013 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:48:02,230 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 05:48:02,470 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:48:02,710 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:48:02,947 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:48:03,184 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:48:03,417 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:48:03,653 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:48:03,893 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:48:04,129 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:48:04,364 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:48:04,598 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:48:30,527 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 05:49:41,891 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 05:50:02,294 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 05:50:02,534 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:02,767 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:03,002 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:03,241 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:03,481 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:03,715 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:03,948 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:04,186 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:04,423 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:04,654 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:14,315 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 05:50:14,557 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:14,795 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:15,033 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:15,267 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:15,503 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:15,739 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:15,982 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:16,223 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:16,460 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:16,697 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:55:27,685 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=domain&id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:55:29,568 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 05:55:29,570 - src.api.routes - INFO - Generating enhanced Excel report for domain: boernevisioncenter.com
2025-06-30 05:55:30,509 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:55:31,024 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:55:31,632 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:55:31,968 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:55:32,215 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:55:32,842 - src.api.routes - INFO - Enhanced Excel report generated: reports\reports_boernevisioncenter_com\seo_report_boernevisioncenter_com_20250630_055529.xlsx
2025-06-30 05:55:36,686 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 05:55:36,923 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:55:37,157 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:55:37,391 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:55:37,626 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:55:37,866 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:55:38,104 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:55:38,343 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:55:38,580 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:55:38,818 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:55:39,052 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:58:54,457 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 05:58:54,691 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:58:54,924 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:58:55,157 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:58:55,392 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:58:55,626 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:58:55,857 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:58:56,097 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:58:56,332 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:58:56,569 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:58:56,802 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:27,111 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=domain&id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:28,934 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 06:00:28,936 - src.api.routes - INFO - Generating enhanced Excel report for domain: boernevisioncenter.com
2025-06-30 06:00:29,184 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:29,671 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:30,245 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:30,580 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:30,817 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:31,443 - src.api.routes - INFO - Enhanced Excel report generated: reports\reports_boernevisioncenter_com\seo_report_boernevisioncenter_com_20250630_060028.xlsx
2025-06-30 06:00:33,439 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 06:00:33,678 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:33,917 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:34,158 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:34,398 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:34,638 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:34,874 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:35,108 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:35,351 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:35,601 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:35,843 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:38,042 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 06:00:38,280 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:38,517 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:38,757 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:39,004 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:39,242 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:39,483 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:39,721 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:40,005 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:40,241 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:40,488 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:05:26,691 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 06:05:41,186 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 06:05:41,434 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:05:41,674 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:05:41,911 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:05:42,145 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:05:42,381 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:05:42,615 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:05:42,852 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:05:43,086 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:05:43,319 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:05:43,559 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:10,028 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=domain&id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:11,812 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 06:06:11,814 - src.api.routes - INFO - Generating enhanced Excel report for domain: boernevisioncenter.com
2025-06-30 06:06:12,162 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:12,678 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:13,256 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:13,865 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:14,111 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:14,707 - src.api.routes - INFO - Enhanced Excel report generated: reports\reports_boernevisioncenter_com\report_boernevisioncenter_com_20250630_060611.xlsx
2025-06-30 06:06:18,693 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 06:06:18,929 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:19,170 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:19,407 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:19,640 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:19,875 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:20,109 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:20,342 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:20,575 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:20,810 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:21,047 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:31,624 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=domain&id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:33,398 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 06:06:33,399 - src.api.routes - INFO - Generating enhanced Excel report for domain: boernevisioncenter.com
2025-06-30 06:06:33,652 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:34,152 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:34,769 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:35,174 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:35,412 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:35,839 - src.api.routes - INFO - Enhanced Excel report generated: reports\reports_boernevisioncenter_com\report_boernevisioncenter_com_20250630_060633.xlsx
2025-06-30 06:08:07,591 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 06:08:07,833 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:08,066 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:08,298 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:08,533 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:08,772 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:09,007 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:09,240 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:09,479 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:09,718 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:09,951 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:14,851 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=domain&id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:16,670 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 06:08:16,672 - src.api.routes - INFO - Generating enhanced Excel report for domain: boernevisioncenter.com
2025-06-30 06:08:16,911 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:17,392 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:18,015 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:18,407 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:18,649 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:19,060 - src.api.routes - INFO - Enhanced Excel report generated: reports\reports_boernevisioncenter_com\report_boernevisioncenter_com_20250630_060816.xlsx
2025-06-30 06:08:22,893 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 06:08:23,133 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:23,367 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:23,621 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:23,853 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:24,090 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:24,325 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:24,557 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:24,790 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:25,025 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:25,260 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:09:21,510 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 06:09:21,747 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:09:21,987 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:09:36,764 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 06:09:37,006 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:09:37,247 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:09:37,718 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:09:37,966 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:09:38,433 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:09:38,678 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:09:38,917 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:09:39,173 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:09:39,411 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:09:39,652 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:11:14,994 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 06:11:34,553 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=domain&id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:11:36,256 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 06:11:36,259 - src.api.routes - INFO - Generating enhanced Excel report for domain: boernevisioncenter.com
2025-06-30 06:11:36,597 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:11:37,115 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:11:37,669 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:11:38,251 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:11:38,499 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:11:39,001 - src.api.routes - INFO - Enhanced Excel report generated: reports\reports_boernevisioncenter_com\report_boernevisioncenter_com_20250630_061136.xlsx
2025-06-30 06:11:59,117 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 06:11:59,352 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:11:59,595 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:11:59,830 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:12:00,063 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:12:00,293 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:12:00,534 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:12:00,773 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:12:01,012 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:12:01,252 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:12:01,495 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:14:15,165 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 06:14:15,402 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:14:15,635 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:14:15,869 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:14:16,111 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:14:16,344 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:14:16,578 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:14:16,810 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:14:17,043 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:14:17,276 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:14:17,518 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:20:57,787 - src.api.app - INFO - FastAPI application created successfully
