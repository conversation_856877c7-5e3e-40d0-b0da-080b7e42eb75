2025-06-30 03:49:18,264 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 03:49:33,807 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 03:49:33,808 - src.cli.main - INFO - Starting API server on 0.0.0.0:8000
2025-06-30 03:50:01,889 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 03:50:01,890 - src.cli.main - INFO - Starting API server on 0.0.0.0:8001
2025-06-30 04:09:00,594 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 04:09:00,595 - src.cli.main - INFO - Starting API server on 0.0.0.0:8001
2025-06-30 04:10:37,429 - src.api.routes - ERROR - Error generating Excel from Supabase for task 713ec8d3-8d06-482a-aaa5-b11346b7aa54
Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_transports\default.py", line 101, in map_httpcore_exceptions
    yield
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_transports\default.py", line 250, in handle_request
    resp = self._pool.handle_request(req)
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_sync\connection_pool.py", line 256, in handle_request
    raise exc from None
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_sync\connection_pool.py", line 236, in handle_request
    response = connection.handle_request(
        pool_request.request
    )
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_sync\connection.py", line 101, in handle_request
    raise exc
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_sync\connection.py", line 78, in handle_request
    stream = self._connect(request)
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_sync\connection.py", line 124, in _connect
    stream = self._network_backend.connect_tcp(**kwargs)
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_backends\sync.py", line 207, in connect_tcp
    with map_exceptions(exc_map):
         ~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1520.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\src\api\routes.py", line 80, in generate_excel_from_supabase
    supabase_client = SupabaseClient(
        url=supabase_url,
        key=supabase_key,
        domain=domain
    )
  File "C:\Gautam\Projects\Scraper\src\database\supabase_client.py", line 31, in __init__
    self.site_id = self._get_or_create_site_id()
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Gautam\Projects\Scraper\src\database\supabase_client.py", line 39, in _get_or_create_site_id
    response = self.client.table('sites').select('id').eq('domain', self.domain).execute()
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\postgrest\_sync\request_builder.py", line 57, in execute
    r = self.session.request(
        self.http_method,
    ...<3 lines>...
        headers=self.headers,
    )
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_client.py", line 825, in request
    return self.send(request, auth=auth, follow_redirects=follow_redirects)
           ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_client.py", line 914, in send
    response = self._send_handling_auth(
        request,
    ...<2 lines>...
        history=[],
    )
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_client.py", line 942, in _send_handling_auth
    response = self._send_handling_redirects(
        request,
        follow_redirects=follow_redirects,
        history=history,
    )
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_client.py", line 979, in _send_handling_redirects
    response = self._send_single_request(request)
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_client.py", line 1014, in _send_single_request
    response = transport.handle_request(request)
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_transports\default.py", line 249, in handle_request
    with map_httpcore_exceptions():
         ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1520.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_transports\default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-06-30 04:10:48,466 - src.services.analysis_service - ERROR - Error in SEO analysis for task 5618550b-d9ad-4f67-abbe-5a360b14e8be
Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\src\services\analysis_service.py", line 58, in run_analysis
    supabase_url = config['supabase_url']  # Required field
                   ~~~~~~^^^^^^^^^^^^^^^^
KeyError: 'supabase_url'
2025-06-30 04:10:48,468 - src.api.routes - ERROR - Error in background analysis task 5618550b-d9ad-4f67-abbe-5a360b14e8be
Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\src\api\routes.py", line 51, in run_seo_analysis
    await analysis_service.run_analysis(
    ...<2 lines>...
    )
  File "C:\Gautam\Projects\Scraper\src\services\analysis_service.py", line 58, in run_analysis
    supabase_url = config['supabase_url']  # Required field
                   ~~~~~~^^^^^^^^^^^^^^^^
KeyError: 'supabase_url'
