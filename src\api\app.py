"""
FastAPI application setup
"""
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse

from src.api.routes import router
from src.config.settings import settings, ensure_directories
from src.utils.logging import get_logger

logger = get_logger(__name__)


def create_app() -> FastAPI:
    """Create and configure the FastAPI application"""
    
    # Ensure required directories exist
    ensure_directories()
    
    # Create FastAPI app
    app = FastAPI(
        title="SEO Data Analysis API",
        description="API for crawling websites, fetching GSC/GA data, and generating SEO reports with Supabase integration.",
        version="2.0.0",
        debug=settings.debug
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Adjust in production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Mount static files (for serving the HTML dashboard)
    app.mount("/static", StaticFiles(directory="public"), name="static")
    
    # Include API routes
    app.include_router(router)
    
    # Serve the main HTML file at the root
    @app.get("/")
    async def serve_dashboard():
        """Serve the main dashboard HTML file"""
        return FileResponse("public/index.html")
    
    # Health check endpoint
    @app.get("/health")
    async def health_check():
        """Health check endpoint"""
        return {"status": "healthy", "version": "2.0.0"}
    
    logger.info("FastAPI application created successfully")
    return app


# Create the app instance
app = create_app()
