"""
WordPress API integration
"""
import requests
from typing import Optional, Dict, Any
from urllib.parse import urlparse

from src.utils.logging import get_logger

logger = get_logger(__name__)


class WordPressAPIClient:
    """Client for WordPress Data Exporter API"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
    
    def detect_wp_api(self, domain: str) -> Optional[str]:
        """Detect WordPress API endpoint for a domain"""
        parsed_domain = urlparse(domain)
        base_url = f"{parsed_domain.scheme}://{parsed_domain.netloc}"
        
        # Common WordPress API endpoints to try
        endpoints = [
            f"{base_url}/wp-json/data-exporter/v1/export",
            f"{base_url}/wp-json/seo-data-exporter/v1/export",
            f"{base_url}/wp-json/wp/v2/",  # Standard WP REST API
        ]
        
        for endpoint in endpoints:
            try:
                response = requests.head(endpoint, timeout=10)
                if response.status_code in [200, 405]:  # 405 is method not allowed but endpoint exists
                    logger.info(f"Found WordPress API endpoint: {endpoint}")
                    return endpoint
            except requests.RequestException:
                continue
        
        logger.warning(f"No WordPress API endpoint found for {domain}")
        return None
    
    def fetch_data(self, wp_api_url: str) -> Optional[Dict[str, Any]]:
        """Fetch data from WordPress API"""
        logger.info(f"Fetching data from WordPress API: {wp_api_url}")
        headers = {'X-Plugin-API-Key': self.api_key}
        
        try:
            response = requests.get(wp_api_url, headers=headers, timeout=60)
            response.raise_for_status()
            logger.info("Successfully fetched data from WordPress API.")
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Could not fetch data from WordPress API: {e}")
            return None
