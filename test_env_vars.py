#!/usr/bin/env python3
"""
Test script to verify that Supabase environment variables are loaded correctly
"""
import sys
import os

# Add src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.config.settings import settings

def test_environment_variables():
    """Test that environment variables are loaded correctly"""
    print("Testing Environment Variable Loading")
    print("=" * 50)
    
    # Test Supabase URL
    print(f"SUPABASE_URL from settings: {settings.supabase_url}")
    print(f"SUPABASE_URL from os.getenv: {os.getenv('SUPABASE_URL')}")
    
    # Test Supabase Key (show only first 20 characters for security)
    supabase_key = settings.supabase_key
    if supabase_key:
        print(f"SUPABASE_KEY from settings: {supabase_key[:20]}...")
    else:
        print("SUPABASE_KEY from settings: None")
    
    env_key = os.getenv('SUPABASE_KEY')
    if env_key:
        print(f"SUPABASE_KEY from os.getenv: {env_key[:20]}...")
    else:
        print("SUPABASE_KEY from os.getenv: None")
    
    print("\nOther Settings:")
    print(f"API_HOST: {settings.api_host}")
    print(f"API_PORT: {settings.api_port}")
    print(f"DEBUG: {settings.debug}")
    print(f"REPORTS_DIR: {settings.reports_dir}")
    
    # Test if Supabase is properly configured
    print("\nSupabase Configuration Status:")
    if settings.supabase_url and settings.supabase_key:
        print("✅ Supabase is properly configured!")
        
        # Test Supabase connection
        try:
            from src.database.supabase_client import SupabaseClient, SUPABASE_AVAILABLE
            if SUPABASE_AVAILABLE:
                print("✅ Supabase client is available")
                # Test creating a client (don't actually connect)
                print("✅ Environment variables are ready for Supabase client initialization")
            else:
                print("❌ Supabase client library not available")
        except Exception as e:
            print(f"❌ Error testing Supabase client: {e}")
    else:
        print("❌ Supabase credentials missing!")
        if not settings.supabase_url:
            print("  - SUPABASE_URL is missing")
        if not settings.supabase_key:
            print("  - SUPABASE_KEY is missing")

if __name__ == "__main__":
    test_environment_variables()
