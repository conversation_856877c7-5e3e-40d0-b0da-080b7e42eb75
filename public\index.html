<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SEO Site Manager</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
  <style>
    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f8f9fa; }
    .card { border-radius: 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); margin-bottom: 20px; }
    .d-none { display: none; }
    .file-upload { position: relative; overflow: hidden; display: inline-block; width: 100%; }
    .file-upload input[type=file] { position: absolute; left: -9999px; }
    .file-upload-name { margin-top: 5px; font-size: 0.875rem; color: #6c757d; }
  </style>
</head>
<body>
  <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container">
      <span class="navbar-brand"><i class="bi bi-graph-up me-2"></i>SEO Site Manager</span>
    </div>
  </nav>

  <div class="container py-4">
    <div class="row">
      <div class="col-lg-8">
        <!-- Sites Overview -->
        <div class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <span><i class="bi bi-building me-2"></i>Your Sites</span>
            <div>
              <button class="btn btn-sm btn-outline-success me-2" onclick="showAddSiteForm();">
                <i class="bi bi-plus-circle me-1"></i>Add New Site
              </button>
              <button class="btn btn-sm btn-outline-primary" onclick="loadSites();">
                <i class="bi bi-arrow-clockwise me-1"></i>Refresh
              </button>
            </div>
          </div>
          <div class="card-body">
            <div id="sitesLoading" class="text-center py-4">
              <div class="spinner-border spinner-border-sm me-2" role="status"></div>
              Loading your sites...
            </div>
            <div id="sitesContainer" class="d-none">
              <div id="sitesList"></div>
            </div>
            <div id="noSites" class="text-center py-4 d-none">
              <i class="bi bi-info-circle me-2 text-muted"></i>
              <span class="text-muted">No sites found. Click "Add New Site" to analyze your first website.</span>
            </div>
          </div>
        </div>

        <!-- Add New Site Form -->
        <div class="card d-none" id="addSiteCard">
          <div class="card-header d-flex justify-content-between align-items-center">
            <span><i class="bi bi-plus-circle me-2"></i>Add New Site</span>
            <button class="btn btn-sm btn-outline-danger" onclick="hideAllCards();">
              <i class="bi bi-x-circle me-1"></i>Cancel
            </button>
          </div>
          <div class="card-body">
            <form id="analysisForm">
              <div class="row mb-3">
                <div class="col-md-6">
                  <label for="domainProperty" class="form-label">Domain Property</label>
                  <input type="text" class="form-control" id="domainProperty" placeholder="https://example.com/" required>
                </div>
                <div class="col-md-6">
                  <label for="gaPropertyId" class="form-label">GA Property ID</label>
                  <input type="text" class="form-control" id="gaPropertyId" placeholder="*********" required>
                </div>
              </div>
              <div class="mb-3">
                <label for="serviceAccountFile" class="form-label">Service Account JSON</label>
                <div class="file-upload">
                  <button type="button" class="btn btn-outline-secondary w-100">
                    <i class="bi bi-file-earmark-text me-2"></i>Choose Service Account File
                  </button>
                  <input type="file" class="form-control" id="serviceAccountFile" accept=".json" required>
                </div>
                <div class="file-upload-name" id="fileUploadName">No file chosen</div>
              </div>
              <div class="row mb-3">
                <div class="col-md-6">
                  <label for="startDate" class="form-label">Start Date</label>
                  <input type="date" class="form-control" id="startDate">
                </div>
                <div class="col-md-6">
                  <label for="endDate" class="form-label">End Date</label>
                  <input type="date" class="form-control" id="endDate">
                </div>
              </div>
              <div class="d-grid">
                <button type="submit" class="btn btn-primary">
                  <i class="bi bi-play-fill me-2"></i>Start Analysis
                </button>
              </div>
            </form>
          </div>
        </div>

        <!-- Task Status -->
        <div class="card d-none" id="taskStatus">
          <div class="card-header">
            <i class="bi bi-gear-fill me-2"></i>Analysis Progress
          </div>
          <div class="card-body">
            <div class="mb-3">
              <div class="d-flex justify-content-between mb-1">
                <span>Progress:</span>
                <span id="progressPercentage">0%</span>
              </div>
              <div class="progress">
                <div class="progress-bar progress-bar-striped progress-bar-animated" id="progressBar" role="progressbar" style="width: 0%"></div>
              </div>
            </div>
            <div class="mb-3">
              <p class="mb-1 fw-bold">Current Task:</p>
              <p id="currentTask">Initializing...</p>
            </div>
            <div class="d-flex justify-content-between">
              <button class="btn btn-outline-secondary" onclick="cancelTask();">
                <i class="bi bi-x-circle me-1"></i>Cancel
              </button>
              <a href="#" class="btn btn-success d-none" id="downloadBtn">
                <i class="bi bi-download me-1"></i>Download Report
              </a>
            </div>
          </div>
        </div>

        <!-- Excel Report Generation -->
        <div class="card">
          <div class="card-header">
            <i class="bi bi-file-earmark-excel me-2"></i>Generate Excel Report
          </div>
          <div class="card-body">
            <form id="excelForm">
              <div class="row mb-3">
                <div class="col-md-6">
                  <label for="siteSelect" class="form-label">Select Site</label>
                  <select class="form-select" id="siteSelect" required>
                    <option value="">Choose a site...</option>
                  </select>
                </div>
                <div class="col-md-6">
                  <label for="reportType" class="form-label">Report Type</label>
                  <select class="form-select" id="reportType">
                    <option value="all">All Data</option>
                    <option value="date_range">Date Range</option>
                    <option value="specific_date">Specific Date</option>
                  </select>
                </div>
              </div>
              
              <div id="dateRangeOptions" class="row mb-3 d-none">
                <div class="col-md-6">
                  <label for="startDateExcel" class="form-label">Start Date</label>
                  <input type="date" class="form-control" id="startDateExcel">
                </div>
                <div class="col-md-6">
                  <label for="endDateExcel" class="form-label">End Date</label>
                  <input type="date" class="form-control" id="endDateExcel">
                </div>
              </div>
              
              <div id="specificDateOption" class="mb-3 d-none">
                <label for="specificDate" class="form-label">Specific Date</label>
                <select class="form-select" id="specificDate">
                  <option value="">Choose a date...</option>
                </select>
              </div>
              
              <div class="d-grid">
                <button type="submit" class="btn btn-success">
                  <i class="bi bi-download me-2"></i>Generate Excel Report
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      <div class="col-lg-4">
        <!-- Quick Actions -->
        <div class="card">
          <div class="card-header">
            <i class="bi bi-lightning me-2"></i>Quick Actions
          </div>
          <div class="card-body">
            <div class="d-grid gap-2">
              <button class="btn btn-outline-primary" onclick="showAddSiteForm();">
                <i class="bi bi-plus-circle me-2"></i>Add New Site
              </button>
            </div>
          </div>
        </div>

        <!-- API Documentation -->
        <div class="card">
          <div class="card-header">
            <i class="bi bi-code-square me-2"></i>API Endpoints
          </div>
          <div class="card-body">
            <h6>Available Endpoints:</h6>
            <ul class="list-unstyled">
              <li><code>GET /sites/</code> - List all sites</li>
              <li><code>POST /generate_report/</code> - Run analysis</li>
              <li><code>POST /generate_excel_enhanced/</code> - Generate Excel</li>
              <li><code>GET /task/{task_id}</code> - Check task status</li>
            </ul>
            <small class="text-muted">All endpoints work independently of this web interface.</small>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    const API_ENDPOINT = 'http://localhost:8000';
    let sitesData = [];

    // Core functions
    function hideAllCards() {
      document.getElementById('addSiteCard').classList.add('d-none');
      document.getElementById('taskStatus').classList.add('d-none');
    }
    
    function showAddSiteForm() {
      hideAllCards();
      document.getElementById('addSiteCard').classList.remove('d-none');
      document.getElementById('addSiteCard').scrollIntoView({ behavior: 'smooth' });
    }

    async function loadSites() {
      try {
        document.getElementById('sitesLoading').classList.remove('d-none');
        document.getElementById('sitesContainer').classList.add('d-none');
        document.getElementById('noSites').classList.add('d-none');
        
        const response = await fetch(`${API_ENDPOINT}/sites/`);
        const data = await response.json();
        sitesData = data.sites || [];
        
        document.getElementById('sitesLoading').classList.add('d-none');
        
        if (sitesData.length === 0) {
          document.getElementById('noSites').classList.remove('d-none');
        } else {
          displaySites(sitesData);
          populateSiteSelect(sitesData);
          document.getElementById('sitesContainer').classList.remove('d-none');
        }
      } catch (error) {
        console.error('Error loading sites:', error);
        document.getElementById('sitesLoading').classList.add('d-none');
        document.getElementById('sitesContainer').innerHTML = `<div class="alert alert-danger">Error loading sites: ${error.message}</div>`;
        document.getElementById('sitesContainer').classList.remove('d-none');
      }
    }

    function displaySites(sites) {
      const sitesList = document.getElementById('sitesList');
      sitesList.innerHTML = '';
      
      sites.forEach(site => {
        const siteCard = document.createElement('div');
        siteCard.className = 'card mb-3 border-0 shadow-sm';
        siteCard.innerHTML = `
          <div class="card-body">
            <div class="row align-items-center">
              <div class="col-md-4">
                <h6 class="card-title mb-1 text-primary">${site.domain}</h6>
                <small class="text-muted">Last updated: ${site.last_updated || 'Never'}</small>
              </div>
              <div class="col-md-4">
                <div class="row text-center">
                  <div class="col"><div class="fw-bold">${site.data_summary.pages}</div><small class="text-muted">Pages</small></div>
                  <div class="col"><div class="fw-bold">${site.data_summary.keywords}</div><small class="text-muted">Keywords</small></div>
                  <div class="col"><div class="fw-bold">${site.data_summary.total_records}</div><small class="text-muted">Total</small></div>
                </div>
              </div>
              <div class="col-md-4 text-end">
                <button class="btn btn-sm btn-outline-success" onclick="generateReportForSite('${site.site_id}')">
                  <i class="bi bi-file-earmark-excel me-1"></i>Generate Report
                </button>
              </div>
            </div>
          </div>
        `;
        sitesList.appendChild(siteCard);
      });
    }

    function populateSiteSelect(sites) {
      const siteSelect = document.getElementById('siteSelect');
      siteSelect.innerHTML = '<option value="">Choose a site...</option>';
      sites.forEach(site => {
        const option = document.createElement('option');
        option.value = site.site_id;
        option.textContent = `${site.domain} (${site.data_summary.total_records} records)`;
        option.dataset.domain = site.domain;
        option.dataset.dates = JSON.stringify(site.available_dates);
        siteSelect.appendChild(option);
      });
    }

    function generateReportForSite(siteId) {
      document.getElementById('siteSelect').value = siteId;
      document.getElementById('reportType').value = 'all';
      document.getElementById('excelForm').scrollIntoView({ behavior: 'smooth' });
    }

    function updateDateOptions() {
      const reportType = document.getElementById('reportType').value;
      const dateRangeOptions = document.getElementById('dateRangeOptions');
      const specificDateOption = document.getElementById('specificDateOption');
      
      dateRangeOptions.classList.add('d-none');
      specificDateOption.classList.add('d-none');
      
      if (reportType === 'date_range') {
        dateRangeOptions.classList.remove('d-none');
      } else if (reportType === 'specific_date') {
        const selectedOption = document.getElementById('siteSelect').options[document.getElementById('siteSelect').selectedIndex];
        if (selectedOption && selectedOption.dataset.dates) {
          const availableDates = JSON.parse(selectedOption.dataset.dates);
          const specificDate = document.getElementById('specificDate');
          specificDate.innerHTML = '<option value="">Choose a date...</option>';
          availableDates.forEach(date => {
            const option = document.createElement('option');
            option.value = date;
            option.textContent = date;
            specificDate.appendChild(option);
          });
          specificDateOption.classList.remove('d-none');
        }
      }
    }

    // Event listeners
    document.addEventListener('DOMContentLoaded', function() {
      // File upload
      document.getElementById('serviceAccountFile').addEventListener('change', function(e) {
        document.getElementById('fileUploadName').textContent = e.target.files[0]?.name || 'No file chosen';
      });

      // Report type change
      document.getElementById('reportType').addEventListener('change', updateDateOptions);
      document.getElementById('siteSelect').addEventListener('change', updateDateOptions);

      // Analysis form
      document.getElementById('analysisForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const serviceAccountFile = document.getElementById('serviceAccountFile').files[0];
        if (!serviceAccountFile) {
          alert('Please select a service account file');
          return;
        }

        const fileReader = new FileReader();
        fileReader.onload = async function(event) {
          try {
            const serviceAccount = JSON.parse(event.target.result);
            
            const formData = {
              domain_property: document.getElementById('domainProperty').value,
              ga_property_id: document.getElementById('gaPropertyId').value,
              start_date: document.getElementById('startDate').value || null,
              end_date: document.getElementById('endDate').value || null
            };

            hideAllCards();
            document.getElementById('taskStatus').classList.remove('d-none');
            
            const response = await fetch(`${API_ENDPOINT}/generate_report_with_service_account/`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ ...formData, service_account_data: serviceAccount })
            });

            const result = await response.json();
            pollTaskStatus(result.task_id);
            
          } catch (error) {
            alert('Error: ' + error.message);
          }
        };
        fileReader.readAsText(serviceAccountFile);
      });

      // Excel form
      document.getElementById('excelForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const siteId = document.getElementById('siteSelect').value;
        if (!siteId) {
          alert('Please select a site');
          return;
        }

        const requestData = { site_id: siteId };
        const reportType = document.getElementById('reportType').value;
        
        if (reportType === 'date_range') {
          const startDate = document.getElementById('startDateExcel').value;
          const endDate = document.getElementById('endDateExcel').value;
          if (startDate) requestData.start_date = startDate;
          if (endDate) requestData.end_date = endDate;
        } else if (reportType === 'specific_date') {
          const specificDate = document.getElementById('specificDate').value;
          if (specificDate) requestData.date = specificDate;
        }

        try {
          const response = await fetch(`${API_ENDPOINT}/generate_excel_enhanced/`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(requestData)
          });

          const result = await response.json();
          document.getElementById('taskStatus').classList.remove('d-none');
          pollTaskStatus(result.task_id);
          
        } catch (error) {
          alert('Error: ' + error.message);
        }
      });

      // Set default dates
      const today = new Date();
      const oneYearAgo = new Date();
      oneYearAgo.setFullYear(today.getFullYear() - 1);
      
      document.getElementById('startDate').value = oneYearAgo.toISOString().split('T')[0];
      document.getElementById('endDate').value = today.toISOString().split('T')[0];

      // Load sites on page load
      loadSites();
    });

    async function pollTaskStatus(taskId) {
      try {
        const response = await fetch(`${API_ENDPOINT}/task/${taskId}`);
        const status = await response.json();
        
        document.getElementById('progressPercentage').textContent = `${status.progress || 0}%`;
        document.getElementById('progressBar').style.width = `${status.progress || 0}%`;
        document.getElementById('currentTask').textContent = status.message || 'Processing...';
        
        if (status.status === 'completed') {
          document.getElementById('progressBar').classList.remove('progress-bar-animated');
          document.getElementById('currentTask').textContent = 'Analysis completed!';
          
          if (status.result && status.result.excel_report) {
            const downloadBtn = document.getElementById('downloadBtn');
            downloadBtn.href = `${API_ENDPOINT}/download/${encodeURIComponent(status.result.excel_report)}`;
            downloadBtn.classList.remove('d-none');
          }
          
          setTimeout(() => {
            loadSites(); // Refresh sites list
          }, 2000);
          
        } else if (status.status === 'failed') {
          document.getElementById('currentTask').textContent = `Error: ${status.error || 'Analysis failed'}`;
          document.getElementById('progressBar').classList.remove('progress-bar-animated');
        } else {
          setTimeout(() => pollTaskStatus(taskId), 2000);
        }
      } catch (error) {
        console.error('Error polling task status:', error);
      }
    }

    function cancelTask() {
      hideAllCards();
    }
  </script>
</body>
</html>
